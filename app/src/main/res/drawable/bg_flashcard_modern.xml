<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Shadow Layer -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="6dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/shadow_medium" />
            <corners android:radius="24dp" />
        </shape>
    </item>
    
    <!-- Main Card Layer -->
    <item android:bottom="4dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/flashcard_surface" />
            <corners android:radius="24dp" />
            <stroke android:width="1dp" android:color="@color/flashcard_surface_variant" />
        </shape>
    </item>
</layer-list>
