<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".screen.home.HomeFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/dp_16">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/dp_24">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_greeting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/greeting_hi"
                    style="@style/TextBold.24sp"
                    android:textColor="@color/black" />

                <TextView
                    android:id="@+id/tv_start_learning"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/start_learning"
                    style="@style/TextRegular.16sp"
                    android:textColor="#6B7280"
                    android:layout_marginTop="@dimen/dp_4" />

            </LinearLayout>

            <ImageView
                android:id="@+id/iv_search"
                android:layout_width="@dimen/dp_48"
                android:layout_height="@dimen/dp_48"
                android:src="@drawable/ic_search"
                android:background="@drawable/bg_blue_button"
                android:padding="@dimen/dp_12"
                android:layout_marginStart="@dimen/dp_16" />

        </LinearLayout>

        <!-- Study Time Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_24"
            app:cardCornerRadius="@dimen/dp_16"
            app:cardElevation="0dp"
            android:background="@drawable/bg_rounded_card">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/dp_20"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/study_time_this_week"
                        style="@style/TextRegular.14sp"
                        android:textColor="#6B7280"
                        android:layout_marginBottom="@dimen/dp_12" />

                    <Button
                        android:id="@+id/btn_lets_start"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/lets_start"
                        android:background="@drawable/bg_blue_button"
                        android:textColor="@color/white"
                        style="@style/TextSemiBold.14sp"
                        android:paddingHorizontal="@dimen/dp_24"
                        android:paddingVertical="@dimen/dp_12"
                        android:textAllCaps="false" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="@dimen/dp_120"
                    android:layout_height="@dimen/dp_120"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/tv_study_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2h 15m"
                        style="@style/TextBold.18sp"
                        android:textColor="@color/main_blue" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Courses Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/dp_16">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/courses"
                style="@style/TextBold.18sp"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_see_all_courses"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/see_all"
                style="@style/TextMedium.14sp"
                android:textColor="@color/main_blue" />

        </LinearLayout>

        <!-- Course Categories RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvCourseCategories"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_32"
            android:clipToPadding="false"
            android:paddingStart="@dimen/dp_8"
            android:paddingEnd="@dimen/dp_8" />

        <!-- Loading indicator for courses -->
        <ProgressBar
            android:id="@+id/pbCoursesLoading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/dp_32"
            android:visibility="gone" />

        <!-- Recently Learned Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/dp_16">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/nearby_learning"
                style="@style/TextBold.18sp"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/tv_see_all_recent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/see_all"
                style="@style/TextMedium.14sp"
                android:textColor="@color/main_blue" />

        </LinearLayout>

        <!-- Recent Learning Cards -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="@dimen/dp_32">

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/dp_8"
                app:cardCornerRadius="@dimen/dp_12"
                app:cardElevation="@dimen/dp_2">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_120"
                        android:src="@drawable/img_ob1"
                        android:scaleType="centerCrop" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="@dimen/dp_12">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/tourist_trip"
                            style="@style/TextSemiBold.14sp"
                            android:textColor="@color/black"
                            android:layout_marginBottom="@dimen/dp_4" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/dp_8">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Lesson: 3"
                                style="@style/TextRegular.12sp"
                                android:textColor="#6B7280" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Advanced: 76"
                                style="@style/TextRegular.12sp"
                                android:textColor="#6B7280"
                                android:layout_marginStart="@dimen/dp_16" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="6/10"
                            style="@style/TextRegular.12sp"
                            android:textColor="#6B7280" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/dp_8"
                app:cardCornerRadius="@dimen/dp_12"
                app:cardElevation="@dimen/dp_2">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp_120"
                        android:src="@drawable/img_ob2"
                        android:scaleType="centerCrop" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="@dimen/dp_12">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/hang_out_with_friends"
                            style="@style/TextSemiBold.14sp"
                            android:textColor="@color/black"
                            android:layout_marginBottom="@dimen/dp_4" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="@dimen/dp_8">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Lesson: 3"
                                style="@style/TextRegular.12sp"
                                android:textColor="#6B7280" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Advanced: 76"
                                style="@style/TextRegular.12sp"
                                android:textColor="#6B7280"
                                android:layout_marginStart="@dimen/dp_16" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="6/10"
                            style="@style/TextRegular.12sp"
                            android:textColor="#6B7280" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- Suggestions Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/dp_16">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/suggestions_for_you"
                style="@style/TextBold.18sp"
                android:textColor="@color/black" />

            <ImageView
                android:id="@+id/iv_refresh"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:src="@drawable/ic_refresh" />

        </LinearLayout>

        <!-- Suggested Course Card -->
        <include
            android:id="@+id/suggested_course_card"
            layout="@layout/item_suggested_course"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dp_16" />

    </LinearLayout>

</ScrollView>
