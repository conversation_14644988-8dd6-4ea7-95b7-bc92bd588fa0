<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Khóa học"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@android:color/black" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Xem toàn bộ →"
                android:textSize="14sp"
                android:textColor="@android:color/holo_blue_dark"
                android:clickable="true"
                android:focusable="true"
                android:background="?android:attr/selectableItemBackground"
                android:padding="8dp" />

        </LinearLayout>

        <!-- Course Categories RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvCourseCategories"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:paddingStart="8dp"
            android:paddingEnd="8dp" />

    </LinearLayout>

    <!-- Loading indicator -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</FrameLayout>