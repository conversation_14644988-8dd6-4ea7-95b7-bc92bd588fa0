<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/dp_16">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="@dimen/dp_24">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/nearby_learning"
            style="@style/TextBold.24sp"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/tv_see_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/see_all"
            style="@style/TextMedium.14sp"
            android:textColor="@color/main_blue" />

    </LinearLayout>

    <!-- Recent Courses RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_recent_courses"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        tools:listitem="@layout/item_recent_course" />

</LinearLayout>
