<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/btn_get_started"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/btn_get_started"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_40"
        android:layout_marginHorizontal="@dimen/dp_20"
        android:background="@drawable/bg_button_onboarding"
        android:fontFamily="@font/nunito_medium"
        android:gravity="center"
        android:text="@string/let_go"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_12"
        app:layout_constraintBottom_toTopOf="@+id/ll_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_8"
        android:layout_marginBottom="@dimen/dp_10"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/nunito_semibold"
            android:text="@string/already_got_an_account"
            android:textColor="#263038"
            android:textSize="@dimen/sp_14" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp_6"
            android:fontFamily="@font/nunito_bold"
            android:text="@string/login"
            android:textColor="@color/main_blue"
            android:textSize="@dimen/sp_14"
            android:textStyle="bold" />
    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.constraintlayout.widget.ConstraintLayout>
