<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/screen_background"
    android:orientation="vertical">

    <!-- Header with back button and title -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="@dimen/dp_16"
        android:background="@color/white">

        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="@dimen/dp_4"
            android:clickable="true"
            android:focusable="true" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Courses"
            android:textAlignment="center"
            android:textSize="@dimen/sp_18"
            android:textColor="@color/black"
            android:fontFamily="@font/nunito_semibold" />

        <!-- Invisible view for balance -->
        <View
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24" />

    </LinearLayout>

    <!-- Tab Layout -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/white"
        android:paddingHorizontal="@dimen/dp_16"
        android:paddingBottom="@dimen/dp_16">

        <TextView
            android:id="@+id/tab_ongoing"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Ongoing"
            android:textAlignment="center"
            android:textSize="@dimen/sp_16"
            android:textColor="@color/tab_text_color"
            android:fontFamily="@font/nunito_medium"
            android:paddingVertical="@dimen/dp_12"
            android:background="@drawable/tab_selector"
            android:clickable="true"
            android:focusable="true" />

        <TextView
            android:id="@+id/tab_completed"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Completed"
            android:textAlignment="center"
            android:textSize="@dimen/sp_16"
            android:textColor="@color/tab_text_inactive"
            android:fontFamily="@font/nunito_medium"
            android:paddingVertical="@dimen/dp_12"
            android:background="@drawable/tab_selector"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_lessons"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:padding="@dimen/dp_16"
        tools:listitem="@layout/item_lesson_card" />

</LinearLayout>
