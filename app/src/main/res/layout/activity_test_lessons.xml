<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="@color/background_color">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Test Lessons Display"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="32dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_show_lessons_from_json"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Show Lessons from JSON"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        app:cornerRadius="12dp"
        style="@style/Widget.Material3.Button" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_back_to_main"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Back to Main"
        android:textSize="16sp"
        app:cornerRadius="12dp"
        style="@style/Widget.Material3.Button.OutlinedButton" />

</LinearLayout>
