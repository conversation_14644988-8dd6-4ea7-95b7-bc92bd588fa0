<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="110dp"
    android:layout_margin="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="8dp">

        <ImageView
            android:id="@+id/ivCategoryIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_book"
            android:layout_marginBottom="6dp"
            android:scaleType="centerCrop" />

        <TextView
            android:id="@+id/tvCategoryTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Travel"
            android:textSize="12sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="2dp"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/tvLessonCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="5 lessons"
            android:textSize="10sp"
            android:textColor="@android:color/darker_gray"
            android:gravity="center" />

    </LinearLayout>

</androidx.cardview.widget.CardView>