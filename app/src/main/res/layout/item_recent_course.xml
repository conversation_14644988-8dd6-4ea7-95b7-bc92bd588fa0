<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp_8"
    app:cardCornerRadius="@dimen/dp_12"
    app:cardElevation="@dimen/dp_2">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/iv_course_image"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_140"
            android:scaleType="centerCrop"
            android:src="@drawable/img_ob1" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp_16">

            <TextView
                android:id="@+id/tv_course_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tourist_trip"
                style="@style/TextSemiBold.16sp"
                android:textColor="@color/black"
                android:layout_marginBottom="@dimen/dp_8" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="@dimen/dp_12">

                <ImageView
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:src="@drawable/ic_home"
                    android:layout_marginEnd="@dimen/dp_4" />

                <TextView
                    android:id="@+id/tv_lesson_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Lesson: 3"
                    style="@style/TextRegular.12sp"
                    android:textColor="#6B7280" />

                <ImageView
                    android:layout_width="@dimen/dp_16"
                    android:layout_height="@dimen/dp_16"
                    android:src="@drawable/ic_me"
                    android:layout_marginStart="@dimen/dp_16"
                    android:layout_marginEnd="@dimen/dp_4" />

                <TextView
                    android:id="@+id/tv_advanced_level"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Advanced: 76"
                    style="@style/TextRegular.12sp"
                    android:textColor="#6B7280" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ProgressBar
                        android:id="@+id/progress_course"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp_6"
                        android:layout_weight="1"
                        android:max="10"
                        android:progress="6"
                        android:progressTint="@color/secondary_orange"
                        android:progressBackgroundTint="#E5E7EB"
                        android:layout_marginEnd="@dimen/dp_8" />

                    <TextView
                        android:id="@+id/tv_progress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="6/10"
                        style="@style/TextRegular.12sp"
                        android:textColor="#6B7280" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
