<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:paddingHorizontal="@dimen/dp_20">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_onboarding_image"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_280"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintDimensionRatio="320:280"
        android:layout_marginTop="@dimen/dp_20"
        tools:src="@drawable/img_ob1"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_onboarding_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_24"
        android:textStyle="bold"
        android:textColor="#1C1C1C"
        app:layout_constraintTop_toBottomOf="@id/iv_onboarding_image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/dp_36"
        android:gravity="center"
        tools:text="Title"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_onboarding_description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_12"
        android:textColor="#7D8388"
        app:layout_constraintTop_toBottomOf="@id/tv_onboarding_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/dp_8"
        android:gravity="center"
        tools:text="Subtitle"/>

</androidx.constraintlayout.widget.ConstraintLayout>
