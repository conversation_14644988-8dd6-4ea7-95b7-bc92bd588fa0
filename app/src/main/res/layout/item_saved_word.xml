<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginBottom="@dimen/dp_8">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="@dimen/dp_12"
        app:cardElevation="@dimen/dp_2"
        android:layout_margin="@dimen/dp_4">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/dp_16"
            android:gravity="center_vertical">

            <!-- Word and definition -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- Word with sound icon -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/dp_4">

                    <TextView
                        android:id="@+id/tv_word"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="as many as"
                        android:textSize="16sp"
                        android:textColor="@android:color/black"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/iv_sound"
                        android:layout_width="@dimen/dp_20"
                        android:layout_height="@dimen/dp_20"
                        android:src="@drawable/ic_sound"
                        android:layout_marginStart="@dimen/dp_8"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:padding="@dimen/dp_2"
                        app:tint="#666666" />

                </LinearLayout>

                <!-- Definition -->
                <TextView
                    android:id="@+id/tv_definition"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="as many as"
                    android:textSize="@dimen/sp_14"
                    android:textColor="@color/text_color_suggest_course" />

            </LinearLayout>

            <!-- Favorite toggle button -->
            <ImageView
                android:id="@+id/iv_favorite"
                android:layout_width="@dimen/dp_24"
                android:layout_height="@dimen/dp_24"
                android:src="@drawable/ic_bookmark"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="@dimen/dp_4"
                app:tint="@color/red" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>



