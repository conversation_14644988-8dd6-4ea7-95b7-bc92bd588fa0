<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".screen.me.ProfileFragment">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp_48"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/profile_title"
        app:titleTextColor="@color/black" />

    <LinearLayout
        android:id="@+id/header"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <ImageView
            android:id="@+id/img_profile_avatar"
            android:layout_width="@dimen/dp_96"
            android:layout_height="@dimen/dp_96"
            android:background="@drawable/bg_avatar"
            android:padding="@dimen/dp_4"
            tools:src="@drawable/ic_me" />

        <TextView
            android:id="@+id/tv_profile_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_12"
            android:fontFamily="@font/nunito_bold"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_16"
            tools:text="User" />

        <TextView
            android:id="@+id/tv_profile_email"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_4"
            android:textColor="@color/gray_dark"
            android:textSize="@dimen/sp_12"
            tools:text="<EMAIL>" />
    </LinearLayout>

    <View
        android:id="@+id/divider_top"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_1"
        android:background="@color/gray_light"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header" />

    <!-- Settings list -->
    <LinearLayout
        android:id="@+id/list_settings"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider_top">

        <!-- Row helper layout -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:src="@drawable/ic_user" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_weight="1"
                android:text="@string/profile_country" />

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/arrright" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/gray_light" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:src="@drawable/ic_language" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_weight="1"
                android:text="@string/profile_native_language" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Việt Nam" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/gray_light" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:src="@drawable/ic_key" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_weight="1"
                android:text="@string/profile_notifications" />

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/arrright" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/gray_light" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:src="@drawable/ic_term" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_weight="1"
                android:text="@string/profile_terms" />

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/arrright" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/gray_light" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:src="@drawable/ic_privacy" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_weight="1"
                android:text="@string/profile_privacy" />

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/arrright" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/gray_light" />

        <LinearLayout
            android:id="@+id/ll_logout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:src="@drawable/logout" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_weight="1"
                android:text="@string/profile_logout"
                android:textColor="@color/state_error" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_1"
            android:background="@color/gray_light" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/dp_16"
            android:paddingEnd="@dimen/dp_16">

            <ImageView
                android:layout_width="@dimen/dp_20"
                android:layout_height="@dimen/dp_20"
                android:src="@drawable/trash" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp_12"
                android:layout_weight="1"
                android:text="@string/profile_delete_account"
                android:textColor="@color/state_error" />
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
