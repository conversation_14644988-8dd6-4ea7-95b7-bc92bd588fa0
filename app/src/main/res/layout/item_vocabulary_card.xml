<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp_16"
    android:layout_marginBottom="@dimen/dp_8"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp_20"
    android:paddingVertical="@dimen/dp_16">

    <!-- Word Name -->
    <TextView
        android:id="@+id/text_word_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:fontFamily="@font/nunito_bold"
        android:textColor="@color/lesson_title_text"
        android:textSize="@dimen/sp_18"
        tools:text="School" />

    <!-- Sound Button -->
    <ImageView
        android:id="@+id/btn_sound"
        android:layout_width="@dimen/dp_32"
        android:layout_height="@dimen/dp_32"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:padding="@dimen/dp_4"
        android:src="@drawable/ic_volume"
        android:tint="@color/main_blue"
        tools:ignore="UseAppTint" />

</LinearLayout>
