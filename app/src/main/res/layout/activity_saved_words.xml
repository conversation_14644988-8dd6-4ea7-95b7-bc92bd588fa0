<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/white">

    <!-- Top App Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_56"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="@dimen/dp_16"
        android:background="@android:color/white">

        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="@dimen/dp_24"
            android:layout_height="@dimen/dp_24"
            android:src="@drawable/ic_arrow_right"
            android:rotation="180"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="@dimen/dp_4" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Your saved Words"
            android:textSize="18sp"
            android:textColor="@android:color/black"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginStart="@dimen/dp_16"
            android:layout_marginEnd="@dimen/dp_40" />

    </LinearLayout>

    <!-- Search Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/dp_16"
        android:background="@android:color/white">

        <EditText
            android:id="@+id/et_search"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:background="@drawable/bg_rounded_card"
            android:hint="Search"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:textColorHint="#999999"
            android:padding="@dimen/dp_12"
            android:drawableStart="@drawable/ic_search"
            android:drawablePadding="@dimen/dp_8"
            android:singleLine="true"
            android:imeOptions="actionSearch" />

    </LinearLayout>

    <!-- Words List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_saved_words"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="@dimen/dp_16"
        android:clipToPadding="false" />

</LinearLayout>


