package com.sun.englishlearning.screen.test

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.sun.englishlearning.databinding.ActivityTestLessonsBinding
import com.sun.englishlearning.screen.lessons.LessonsListActivity

/**
 * Simple test activity to test lessons display functionality
 * This activity provides buttons to test different ways of displaying lessons
 */
class TestLessonsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityTestLessonsBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTestLessonsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupViews()
    }

    private fun setupViews() {
        binding.apply {
            btnShowLessonsFromJson.setOnClickListener {
                // Launch LessonsListActivity to show lessons from JSON
                val intent = Intent(this@TestLessonsActivity, LessonsListActivity::class.java)
                startActivity(intent)
            }

            btnBackToMain.setOnClickListener {
                finish()
            }
        }
    }
}
