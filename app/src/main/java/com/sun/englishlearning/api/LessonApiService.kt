package com.sun.englishlearning.api

import com.sun.englishlearning.data.model.Lesson
import com.sun.englishlearning.data.repository.LessonRepository
import com.sun.englishlearning.data.repository.LessonRepositoryImpl
import com.sun.englishlearning.data.repository.UserLessonProgressRepositoryImpl

/**
 * Simple API service to fetch lessons from Firebase
 * This wraps the repository pattern for easier usage
 */
class LessonApiService {
    
    private val userProgressRepository = UserLessonProgressRepositoryImpl()
    private val lessonRepository = LessonRepositoryImpl( userProgressRepository)
    
    /**
     * Get all lessons from Firebase
     * @return Result containing list of lessons or error
     */
    suspend fun getAllLessons(): Result<List<Lesson>> {
        return lessonRepository.getAllLessons()
    }
}